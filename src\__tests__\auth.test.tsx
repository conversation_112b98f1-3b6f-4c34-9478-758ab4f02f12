import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import LoginPage from '../app/[locale]/(auth)/login/page'
import RegisterPage from '../app/[locale]/(auth)/register/page'

// Mock the Supabase client
const mockSignInWithPassword = jest.fn()
const mockSignUp = jest.fn()
const mockFrom = jest.fn()
const mockSelect = jest.fn()
const mockEq = jest.fn()
const mockSingle = jest.fn()

jest.mock('@/lib/supabase/client', () => ({
  createClient: jest.fn(() => ({
    auth: {
      signInWithPassword: mockSignInWithPassword,
      signUp: mockSignUp,
    },
    from: mockFrom.mockReturnValue({
      select: mockSelect.mockReturnValue({
        eq: mockEq.mockReturnValue({
          single: mockSingle.mockResolvedValue({ data: null, error: null })
        })
      }),
      insert: jest.fn().mockResolvedValue({ error: null })
    })
  }))
}))

// Mock Next.js router
const mockPush = jest.fn()
const mockRefresh = jest.fn()

jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
    refresh: mockRefresh,
  }),
}))

// Mock next-intl
jest.mock('next-intl', () => ({
  useTranslations: (namespace: string) => (key: string) => {
    const translations: Record<string, Record<string, string | Record<string, string>>> = {
      'auth.login': {
        title: 'Accedi',
        subtitle: 'Accedi al tuo account',
        email: 'E-mail',
        emailPlaceholder: '<EMAIL>',
        password: 'Password',
        submit: 'Accedi',
        noAccount: 'Non hai ancora un account?',
        signUp: 'Registrati',
        forgotPassword: 'Password dimenticata?',
        error: 'Accesso fallito',
        errors: {
          generic: 'Si è verificato un errore. Riprova più tardi',
          invalidCredentials: 'Email o password non corretti'
        }
      },
      'auth.register': {
        title: 'Registrati',
        subtitle: 'Crea il tuo nuovo account',
        firstName: 'Nome',
        lastName: 'Cognome',
        email: 'E-mail',
        phone: 'Telefono (opzionale)',
        password: 'Password',
        confirmPassword: 'Conferma password',
        submit: 'Registrati',
        hasAccount: 'Hai già un account?',
        signIn: 'Accedi',
        firstNamePlaceholder: 'Mario',
        lastNamePlaceholder: 'Rossi',
        emailPlaceholder: '<EMAIL>',
        passwordPlaceholder: 'Almeno 6 caratteri',
        confirmPasswordPlaceholder: 'Conferma password',
        errors: {
          generic: 'Errore durante la registrazione. Riprova più tardi',
          passwordMismatch: 'Le password non corrispondono',
          passwordTooShort: 'La password deve essere di almeno 6 caratteri',
          profileCreationFailed: 'Errore nella creazione del profilo'
        }
      }
    }
    // Handle nested keys like 'errors.generic'
    if (key.includes('.')) {
      const keyParts = key.split('.')
      let result = translations[namespace]
      for (const part of keyParts) {
        result = result?.[part]
      }
      return result || key
    }
    return translations[namespace]?.[key] || key
  },
  useLocale: () => 'de'
}))

// Mock auth error utility
jest.mock('@/lib/auth-errors', () => ({
  getAuthErrorKey: (errorMessage: string) => {
    if (errorMessage.toLowerCase().includes('invalid credentials')) {
      return `errors.invalidCredentials`
    }
    return `errors.generic`
  }
}))

describe('Authentication Pages', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockFrom.mockReturnValue({
      select: mockSelect.mockReturnValue({
        eq: mockEq.mockReturnValue({
          single: mockSingle.mockResolvedValue({ data: null, error: null })
        })
      }),
      insert: jest.fn().mockResolvedValue({ error: null })
    })
  })

  describe('Login Page', () => {
    it('renders login form correctly', () => {
      render(<LoginPage />)

      expect(screen.getByRole('heading', { name: 'Accedi' })).toBeInTheDocument()
      expect(screen.getByText('Accedi al tuo account')).toBeInTheDocument()
      expect(screen.getByLabelText('E-mail')).toBeInTheDocument()
      expect(screen.getByLabelText('Password')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: 'Accedi' })).toBeInTheDocument()
    })

    it('shows validation error for empty fields', async () => {
      render(<LoginPage />)

      const submitButton = screen.getByRole('button', { name: 'Accedi' })
      fireEvent.click(submitButton)

      // HTML5 validation should prevent submission
      const emailInput = screen.getByLabelText('E-mail')
      expect(emailInput).toBeRequired()
    })

    it('calls signInWithPassword on form submission', async () => {
      mockSignInWithPassword.mockResolvedValue({
        data: { user: { id: 'test-user-id' } },
        error: null
      })

      render(<LoginPage />)

      const emailInput = screen.getByLabelText('E-mail')
      const passwordInput = screen.getByLabelText('Password')
      const submitButton = screen.getByRole('button', { name: 'Accedi' })

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
      fireEvent.change(passwordInput, { target: { value: 'password123' } })
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(mockSignInWithPassword).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password123',
        })
      })
    })

    it('displays error message on login failure', async () => {
      mockSignInWithPassword.mockResolvedValue({
        error: { message: 'Invalid credentials' }
      })

      render(<LoginPage />)

      const emailInput = screen.getByLabelText('E-mail')
      const passwordInput = screen.getByLabelText('Password')
      const submitButton = screen.getByRole('button', { name: 'Accedi' })

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
      fireEvent.change(passwordInput, { target: { value: 'wrongpassword' } })
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('Email o password non corretti')).toBeInTheDocument()
      })
    })
  })

  describe('Register Page', () => {
    it('renders registration form correctly', () => {
      render(<RegisterPage />)

      expect(screen.getByRole('heading', { name: 'Registrati' })).toBeInTheDocument()
      expect(screen.getByText('Crea il tuo nuovo account')).toBeInTheDocument()
      expect(screen.getByText(/Nome/)).toBeInTheDocument()
      expect(screen.getByText(/Cognome/)).toBeInTheDocument()
      expect(screen.getByText(/E-mail/)).toBeInTheDocument()
      expect(screen.getByText('Telefono (opzionale)')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: 'Registrati' })).toBeInTheDocument()
    })

    it('has password input fields', () => {
      render(<RegisterPage />)

      const passwordInput = screen.getByPlaceholderText('Almeno 6 caratteri')
      const confirmPasswordInput = screen.getByPlaceholderText('Conferma password')

      expect(passwordInput).toBeInTheDocument()
      expect(confirmPasswordInput).toBeInTheDocument()
      expect(passwordInput).toHaveAttribute('type', 'password')
      expect(confirmPasswordInput).toHaveAttribute('type', 'password')
    })

    it('calls signUp on successful form submission', async () => {
      mockSignUp.mockResolvedValue({
        data: { user: { id: 'test-user-id' } },
        error: null
      })

      render(<RegisterPage />)

      // Fill out the form
      fireEvent.change(screen.getByPlaceholderText('Mario'), { target: { value: 'Mario' } })
      fireEvent.change(screen.getByPlaceholderText('Rossi'), { target: { value: 'Rossi' } })
      fireEvent.change(screen.getByPlaceholderText('<EMAIL>'), { target: { value: '<EMAIL>' } })
      fireEvent.change(screen.getByPlaceholderText('Almeno 6 caratteri'), { target: { value: 'password123' } })
      fireEvent.change(screen.getByPlaceholderText('Conferma password'), { target: { value: 'password123' } })

      const submitButton = screen.getByRole('button', { name: 'Registrati' })
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(mockSignUp).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password123',
          options: {
            data: {
              first_name: 'Mario',
              last_name: 'Rossi',
              phone: '',
              preferred_language: 'de',
            }
          }
        })
      })
    })
  })
})
