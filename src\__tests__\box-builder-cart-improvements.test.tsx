import fs from 'fs'
import path from 'path'
import germanTranslations from '../messages/de.json'
import italianTranslations from '../messages/it.json'
import frenchTranslations from '../messages/fr.json'

/**
 * Box Builder Cart Improvements Test
 *
 * This test verifies that the box builder cart functionality has been improved:
 * 1. Individual "Add" buttons immediately add items to cart
 * 2. Orange button redirects to cart instead of adding items
 * 3. Proper error handling and user feedback
 */

describe('Box Builder Cart Improvements', () => {
  it('should have the correct translation keys for goToCart functionality', () => {
    // Test that the new translation keys exist
    expect(germanTranslations.common.goToCart).toBe('Zum Warenkorb')
    expect(italianTranslations.common.goToCart).toBe('Vai al carrello')
    expect(frenchTranslations.common.goToCart).toBe('Aller au panier')
  })

  it('should verify that addProduct function is async in the component', () => {
    // Read the component file to verify the changes
    const componentPath = path.join(__dirname, '../components/coffee-box-builder/coffee-box-builder.tsx')
    const componentContent = fs.readFileSync(componentPath, 'utf8')

    // Check that addProduct is now async
    expect(componentContent).toMatch(/const addProduct = async \(product: Product\)/)

    // Check that it calls cartManager.addToCart
    expect(componentContent).toMatch(/await cartManager\.addToCart\(product\.id, 1, user\?\.id\)/)

    // Check that the orange button now calls goToCart
    expect(componentContent).toMatch(/onClick={goToCart}/)

    // Check that goToCart function exists and redirects to cart
    expect(componentContent).toMatch(/const goToCart = \(\) => {/)
    expect(componentContent).toMatch(/router\.push\(`\/\${locale}\/cart`\)/)
  })

  it('should verify that the orange button text uses goToCart translation', () => {
    const componentPath = path.join(__dirname, '../components/coffee-box-builder/coffee-box-builder.tsx')
    const componentContent = fs.readFileSync(componentPath, 'utf8')

    // Check that the button text uses the goToCart translation
    expect(componentContent).toMatch(/{t\('goToCart'\)}/)
  })

  it('should verify that updateQuantity function is async and handles cart updates', () => {
    const componentPath = path.join(__dirname, '../components/coffee-box-builder/coffee-box-builder.tsx')
    const componentContent = fs.readFileSync(componentPath, 'utf8')

    // Check that updateQuantity is now async
    expect(componentContent).toMatch(/const updateQuantity = async \(productId: string, quantity: number\)/)

    // Check that it handles cart updates when increasing quantity
    expect(componentContent).toMatch(/await cartManager\.addToCart\(productId, quantityDiff, user\?\.id\)/)
  })

  it('should verify that cart synchronization is implemented', () => {
    const componentPath = path.join(__dirname, '../components/coffee-box-builder/coffee-box-builder.tsx')
    const componentContent = fs.readFileSync(componentPath, 'utf8')

    // Check that useCart hook is imported and used
    expect(componentContent).toMatch(/import.*useCart.*from.*@\/lib\/cart/)
    expect(componentContent).toMatch(/const.*{.*cart.*}.*=.*useCart\(\)/)

    // Check that there's a useEffect for cart synchronization
    expect(componentContent).toMatch(/useEffect\(\(\) => {[\s\S]*cart.*items[\s\S]*setSelectedItems/)
  })
})
