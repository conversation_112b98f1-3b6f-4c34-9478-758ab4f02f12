import { getUserLevel, getPointsPerCHF } from '@/lib/gamification'

// Mock Supabase
jest.mock('@/lib/supabase/server', () => ({
  createClient: jest.fn(() => ({
    auth: {
      getUser: jest.fn(() => Promise.resolve({
        data: { user: { id: 'test-user-id' } }
      }))
    },
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(() => Promise.resolve({
            data: { total_points: 150, lifetime_spend: 100 }
          }))
        })),
        gt: jest.fn(() => ({
          order: jest.fn(() => ({
            limit: jest.fn(() => ({
              single: jest.fn(() => Promise.resolve({
                data: { threshold_amount: 200 }
              }))
            }))
          }))
        }))
      }))
    }))
  }))
}))

// Mock admin client
jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => ({
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        lte: jest.fn(() => ({
          order: jest.fn(() => ({
            limit: jest.fn(() => Promise.resolve({
              data: [{
                id: '1',
                level: 2,
                name: 'Silver',
                minimum_points: 100,
                discount_percentage: 5,
                points_multiplier: 1.2
              }]
            }))
          }))
        })),
        maybeSingle: jest.fn(() => Promise.resolve({
          data: { points_per_chf: 1.0 }
        }))
      }))
    }))
  }))
}))

describe('Loyalty Profile Enhancements', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('getUserLevel', () => {
    it('should return correct user level based on points', async () => {
      const level = await getUserLevel(150)
      
      expect(level).toBeDefined()
      expect(level?.name).toBe('Silver')
      expect(level?.discount_percentage).toBe(5)
      expect(level?.points_multiplier).toBe(1.2)
    })
  })

  describe('getPointsPerCHF', () => {
    it('should return default points per CHF', async () => {
      const pointsPerCHF = await getPointsPerCHF()
      expect(pointsPerCHF).toBe(1.0)
    })
  })

  describe('Loyalty API calculations', () => {
    it('should calculate points to next gift correctly', () => {
      const userPoints = 150
      const pointsPerCHF = 1.0
      const equivalentSpendAmount = userPoints / pointsPerCHF // 150 CHF
      const nextGiftThreshold = 200 // CHF
      const pointsToNextGift = Math.ceil((nextGiftThreshold - equivalentSpendAmount) * pointsPerCHF)
      
      expect(pointsToNextGift).toBe(50)
    })

    it('should handle case when user has enough points for all gifts', () => {
      const userPoints = 500
      const pointsPerCHF = 1.0
      const equivalentSpendAmount = userPoints / pointsPerCHF // 500 CHF
      const nextGiftThreshold = null // No more gifts available
      const pointsToNextGift = nextGiftThreshold 
        ? Math.ceil((nextGiftThreshold - equivalentSpendAmount) * pointsPerCHF)
        : 0
      
      expect(pointsToNextGift).toBe(0)
    })
  })
})
