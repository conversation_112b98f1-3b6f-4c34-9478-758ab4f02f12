'use client'

import { useEffect, useState, useCallback, useMemo } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useTranslations, useLocale } from 'next-intl'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { formatCurrency } from '@/lib/utils'
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  Calendar,
  BarChart3
} from 'lucide-react'
import { useRouter } from 'next/navigation'
import { AdminBackButton } from '@/components/admin/admin-back-button'
import { AnalyticsOverview } from '@/components/admin/analytics/analytics-overview'

export default function AdminAnalyticsPage() {
  const locale = useLocale()
  const router = useRouter()
  const t = useTranslations('admin')
  const supabase = useMemo(() => createClient(), [])

  const [loading, setLoading] = useState(true)
  const [authChecked, setAuthChecked] = useState(false)
  const [currentRevenue, setCurrentRevenue] = useState(0)
  const [lastRevenue, setLastRevenue] = useState(0)
  const [currentOrderCount, setCurrentOrderCount] = useState(0)
  const [lastOrderCount, setLastOrderCount] = useState(0)
  const [newCustomersThisMonth, setNewCustomersThisMonth] = useState(0)
  const [newCustomersLastMonth, setNewCustomersLastMonth] = useState(0)
  const [avgOrderValue, setAvgOrderValue] = useState(0)
  const [topProductsList, setTopProductsList] = useState<Array<{
    name: string;
    quantity: number;
    revenue: number;
    price: number;
  }>>([])

  const loadAnalyticsData = useCallback(async () => {
    try {
      // Calculate date ranges
      const now = new Date()
      const currentMonth = new Date(now.getFullYear(), now.getMonth(), 1)
      const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)

      // Get current month data (exclude pending and cancelled orders)
      const { data: currentMonthOrders, error: currentOrdersError } = await supabase
        .from('orders')
        .select('total_amount, created_at')
        .gte('created_at', currentMonth.toISOString())
        .not('status', 'in', '(pending,cancelled)')

      if (currentOrdersError) {
        console.error('Error loading current month orders:', currentOrdersError)
      }

      // Get last month data (exclude pending and cancelled orders)
      const { data: lastMonthOrders, error: lastOrdersError } = await supabase
        .from('orders')
        .select('total_amount, created_at')
        .gte('created_at', lastMonth.toISOString())
        .lt('created_at', currentMonth.toISOString())
        .not('status', 'in', '(pending,cancelled)')

      if (lastOrdersError) {
        console.error('Error loading last month orders:', lastOrdersError)
      }

      // Get new customers this month
      const { count: newCustomersThisMonthCount, error: currentCustomersError } = await supabase
        .from('users')
        .select('*', { count: 'exact', head: true })
        .eq('is_admin', false)
        .gte('created_at', currentMonth.toISOString())

      if (currentCustomersError) {
        console.error('Error loading current month customers:', currentCustomersError)
      }

      // Get new customers last month
      const { count: newCustomersLastMonthCount, error: lastCustomersError } = await supabase
        .from('users')
        .select('*', { count: 'exact', head: true })
        .eq('is_admin', false)
        .gte('created_at', lastMonth.toISOString())
        .lt('created_at', currentMonth.toISOString())

      if (lastCustomersError) {
        console.error('Error loading last month customers:', lastCustomersError)
      }

      // Get top products
      const { data: topProducts, error: topProductsError } = await supabase
        .from('order_items')
        .select(`
          product_id,
          quantity,
          products!inner(
            name,
            price
          )
        `)
        .gte('created_at', currentMonth.toISOString())

      if (topProductsError) {
        console.error('Error loading top products:', topProductsError)
      }

      // Calculate statistics
      const currentRevenueCalc = currentMonthOrders?.reduce((sum, order) => sum + order.total_amount, 0) || 0
      const lastRevenueCalc = lastMonthOrders?.reduce((sum, order) => sum + order.total_amount, 0) || 0

      const currentOrderCountCalc = currentMonthOrders?.length || 0
      const lastOrderCountCalc = lastMonthOrders?.length || 0

      const avgOrderValueCalc = currentOrderCountCalc > 0 ? currentRevenueCalc / currentOrderCountCalc : 0

      // Process top products
      const productSales = topProducts?.reduce((acc: Record<string, { name: string; quantity: number; revenue: number; price: number }>, item) => {
        const product = Array.isArray(item.products) ? item.products[0] : item.products as { name: string; price: number }
        const productId = item.product_id

        if (!acc[productId] && product) {
          acc[productId] = {
            name: product.name,
            quantity: 0,
            revenue: 0,
            price: product.price
          }
        }

        if (acc[productId] && product) {
          acc[productId].quantity += item.quantity
          acc[productId].revenue += item.quantity * product.price
        }

        return acc
      }, {}) || {}

      const topProductsListCalc = Object.values(productSales)
        .sort((a, b) => b.revenue - a.revenue)
        .slice(0, 5)

      // Set all state
      setCurrentRevenue(currentRevenueCalc)
      setLastRevenue(lastRevenueCalc)
      setCurrentOrderCount(currentOrderCountCalc)
      setLastOrderCount(lastOrderCountCalc)
      setNewCustomersThisMonth(newCustomersThisMonthCount || 0)
      setNewCustomersLastMonth(newCustomersLastMonthCount || 0)
      setAvgOrderValue(avgOrderValueCalc)
      setTopProductsList(topProductsListCalc)
      setLoading(false)
    } catch (error) {
      console.error('Error loading analytics data:', error)
      setLoading(false)
    }
  }, [supabase])

  useEffect(() => {
    async function checkAuthAndLoadData() {
      try {
        if (authChecked) return

        const { data: { user }, error: authError } = await supabase.auth.getUser()

        if (authError) {
          console.error('Auth error:', authError)
          setAuthChecked(true)
          router.push(`/${locale}/login`)
          return
        }

        if (!user) {
          setAuthChecked(true)
          router.push(`/${locale}/login`)
          return
        }

        const { data: profile, error: profileError } = await supabase
          .from('users')
          .select('is_admin')
          .eq('id', user.id)
          .single()

        if (profileError) {
          console.error('Profile error:', profileError)
          setAuthChecked(true)
          setLoading(false)
          return
        }

        if (!profile?.is_admin) {
          setAuthChecked(true)
          router.push(`/${locale}`)
          return
        }
        setAuthChecked(true)
        await loadAnalyticsData()
      } catch (error) {
        console.error('Error in checkAuthAndLoadData:', error)
        setAuthChecked(true)
        setLoading(false)
      }
    }

    if (!authChecked) {
      checkAuthAndLoadData()
    }
  }, [authChecked, locale, router, supabase, loadAnalyticsData])

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Caricamento analisi...</p>
        </div>
      </div>
    )
  }

  // Calculate growth percentages
  const revenueGrowth = lastRevenue > 0 ? ((currentRevenue - lastRevenue) / lastRevenue) * 100 : 0
  const orderGrowth = lastOrderCount > 0 ? ((currentOrderCount - lastOrderCount) / lastOrderCount) * 100 : 0
  const customerGrowth = newCustomersLastMonth > 0
    ? ((newCustomersThisMonth - newCustomersLastMonth) / newCustomersLastMonth) * 100
    : 0

  return (
    <div className="container mx-auto py-8">
      {/* Back to Dashboard Button */}
      <div className="mb-6">
        <AdminBackButton />
      </div>

      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">{t('analyticsPage.title')}</h1>
          <p className="text-muted-foreground">
            {t('analyticsPage.subtitle')}
          </p>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('monthlyRevenue')}</p>
                <p className="text-2xl font-bold">{formatCurrency(currentRevenue)}</p>
                <div className="flex items-center gap-1 mt-1">
                  {revenueGrowth >= 0 ? (
                    <TrendingUp className="h-4 w-4 text-green-500" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-500" />
                  )}
                  <span className={`text-sm ${revenueGrowth >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                    {Math.abs(revenueGrowth).toFixed(1)}%
                  </span>
                </div>
              </div>
              <DollarSign className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('monthlyOrders')}</p>
                <p className="text-2xl font-bold">{currentOrderCount}</p>
                <div className="flex items-center gap-1 mt-1">
                  {orderGrowth >= 0 ? (
                    <TrendingUp className="h-4 w-4 text-green-500" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-500" />
                  )}
                  <span className={`text-sm ${orderGrowth >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                    {Math.abs(orderGrowth).toFixed(1)}%
                  </span>
                </div>
              </div>
              <ShoppingCart className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('analyticsPage.newCustomersMonth')}</p>
                <p className="text-2xl font-bold">{newCustomersThisMonth}</p>
                <div className="flex items-center gap-1 mt-1">
                  {customerGrowth >= 0 ? (
                    <TrendingUp className="h-4 w-4 text-green-500" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-500" />
                  )}
                  <span className={`text-sm ${customerGrowth >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                    {Math.abs(customerGrowth).toFixed(1)}%
                  </span>
                </div>
              </div>
              <Users className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('avgOrderValue')}</p>
                <p className="text-2xl font-bold">{formatCurrency(avgOrderValue)}</p>
                <p className="text-sm text-muted-foreground mt-1">
                  {t('analyticsPage.currentMonth')}
                </p>
              </div>
              <BarChart3 className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid lg:grid-cols-2 gap-8">
        {/* Top Products */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              {t('analyticsPage.topProductsMonth')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topProductsList.map((product, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <div className="font-medium">{product.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {product.quantity} {t('analyticsPage.sold')}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold">{formatCurrency(product.revenue)}</div>
                    <div className="text-sm text-muted-foreground">
                      {formatCurrency(product.price)} / {t('analyticsPage.piece')}
                    </div>
                  </div>
                </div>
              ))}
              {topProductsList.length === 0 && (
                <p className="text-muted-foreground text-center py-4">
                  {t('analyticsPage.noSalesThisMonth')}
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Monthly Comparison */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              {t('analyticsPage.monthlyComparison')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center p-3 border rounded-lg">
                <div>
                  <div className="font-medium">{t('analyticsPage.revenue')}</div>
                  <div className="text-sm text-muted-foreground">{t('analyticsPage.currentVsLastMonth')}</div>
                </div>
                <div className="text-right">
                  <div className="font-semibold">{formatCurrency(currentRevenue)}</div>
                  <div className="text-sm text-muted-foreground">
                    {t('analyticsPage.vs')} {formatCurrency(lastRevenue)}
                  </div>
                </div>
              </div>

              <div className="flex justify-between items-center p-3 border rounded-lg">
                <div>
                  <div className="font-medium">{t('orders')}</div>
                  <div className="text-sm text-muted-foreground">{t('analyticsPage.currentVsLastMonth')}</div>
                </div>
                <div className="text-right">
                  <div className="font-semibold">{currentOrderCount}</div>
                  <div className="text-sm text-muted-foreground">
                    {t('analyticsPage.vs')} {lastOrderCount}
                  </div>
                </div>
              </div>

              <div className="flex justify-between items-center p-3 border rounded-lg">
                <div>
                  <div className="font-medium">{t('analyticsPage.newCustomers')}</div>
                  <div className="text-sm text-muted-foreground">{t('analyticsPage.currentVsLastMonth')}</div>
                </div>
                <div className="text-right">
                  <div className="font-semibold">{newCustomersThisMonth}</div>
                  <div className="text-sm text-muted-foreground">
                    {t('analyticsPage.vs')} {newCustomersLastMonth}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Visitor Analytics Section */}
      <div className="mt-12">
        <AnalyticsOverview />
      </div>
    </div>
  )
}
