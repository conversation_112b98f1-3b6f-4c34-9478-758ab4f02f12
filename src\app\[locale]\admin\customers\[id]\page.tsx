'use client'

import { useState, useEffect, useMemo } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useLocale, useTranslations } from 'next-intl'
import { createClient } from '@/lib/supabase/client'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { ArrowLeft, User, Mail, Phone, Calendar, Crown, Star, Package, TrendingUp, MapPin, Building } from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import Link from 'next/link'

interface CustomerOrder {
  id: string
  order_number?: string
  status: string
  total_amount: number
  currency: string
  created_at: string
}

interface UserAddress {
  id: string
  user_id: string
  type: 'billing' | 'shipping'
  first_name: string
  last_name: string
  company?: string
  street_address: string
  city: string
  postal_code: string
  country: string
  is_default: boolean
  created_at: string
  updated_at: string
}

interface Customer {
  id: string
  first_name: string
  last_name: string
  email: string
  phone?: string
  created_at: string
  lifetime_spend: number
  current_level: number
  total_points: number
  is_admin: boolean
}

export default function AdminCustomerDetailPage() {
  const locale = useLocale()
  const router = useRouter()
  const params = useParams()
  const t = useTranslations('admin')
  const supabase = useMemo(() => createClient(), [])

  const [loading, setLoading] = useState(true)
  const [authChecked, setAuthChecked] = useState(false)
  const [customer, setCustomer] = useState<Customer | null>(null)
  const [orders, setOrders] = useState<CustomerOrder[]>([])
  const [addresses, setAddresses] = useState<UserAddress[]>([])
  const [stats, setStats] = useState({
    totalOrders: 0,
    totalSpent: 0,
    avgOrderValue: 0,
    lastOrderDate: null as Date | null
  })

  const customerId = params?.id as string

  // Check authentication and admin status
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser()
        
        if (!user) {
          router.push(`/${locale}/login`)
          return
        }

        const { data: profile } = await supabase
          .from('users')
          .select('is_admin')
          .eq('id', user.id)
          .single()

        if (!profile?.is_admin) {
          router.push(`/${locale}`)
          return
        }

        setAuthChecked(true)
      } catch (error) {
        console.error('Auth check error:', error)
        router.push(`/${locale}/login`)
      }
    }

    checkAuth()
  }, [supabase, router, locale])

  // Fetch customer data
  useEffect(() => {
    if (!authChecked || !customerId) return

    const fetchCustomerData = async () => {
      try {
        setLoading(true)

        // Fetch customer details
        const { data: customerData, error: customerError } = await supabase
          .from('users')
          .select('*')
          .eq('id', customerId)
          .single()

        if (customerError) {
          console.error('Error fetching customer:', customerError)
          router.push(`/${locale}/admin/customers`)
          return
        }

        setCustomer(customerData)

        // Fetch customer orders
        const { data: ordersData, error: ordersError } = await supabase
          .from('orders')
          .select('id, order_number, status, total_amount, currency, created_at')
          .eq('user_id', customerId)
          .order('created_at', { ascending: false })

        if (ordersError) {
          console.error('Error fetching orders:', ordersError)
        } else {
          setOrders(ordersData || [])

          // Calculate stats (exclude pending and cancelled orders from financial calculations)
          const completedOrders = ordersData?.filter(order => order.status !== 'pending' && order.status !== 'cancelled') || []
          const totalOrders = completedOrders.length
          const totalSpent = completedOrders.reduce((sum, order) => sum + order.total_amount, 0)
          const avgOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0
          const lastOrderDate = ordersData?.[0] ? new Date(ordersData[0].created_at) : null

          setStats({
            totalOrders,
            totalSpent,
            avgOrderValue,
            lastOrderDate
          })
        }

        // Fetch customer addresses
        const { data: addressesData, error: addressesError } = await supabase
          .from('user_addresses')
          .select('*')
          .eq('user_id', customerId)
          .order('created_at', { ascending: false })

        if (addressesError) {
          console.error('Error fetching addresses:', addressesError)
        } else {
          setAddresses(addressesData || [])
        }
      } catch (error) {
        console.error('Error fetching customer data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchCustomerData()
  }, [authChecked, customerId, supabase, router, locale])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'delivered':
        return 'bg-green-100 text-green-800'
      case 'shipped':
        return 'bg-blue-100 text-blue-800'
      case 'processing':
        return 'bg-yellow-100 text-yellow-800'
      case 'confirmed':
        return 'bg-purple-100 text-purple-800'
      case 'pending':
        return 'bg-orange-100 text-orange-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading || !authChecked) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">Caricamento...</div>
      </div>
    )
  }

  if (!customer) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">Cliente non trovato</div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Button variant="ghost" size="sm" asChild>
              <Link href={`/${locale}/admin/customers`}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                {t('backToDashboard')}
              </Link>
            </Button>
          </div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            {customer.first_name} {customer.last_name}
            {customer.current_level >= 3 && <Crown className="h-6 w-6 text-yellow-500" />}
            {customer.is_admin && <Star className="h-6 w-6 text-blue-500" />}
          </h1>
          <p className="text-muted-foreground">{t('customersPage.customerDetails')}</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Customer Information */}
        <div className="lg:col-span-1 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                {t('customersPage.customerInfo')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  {t('customersPage.fullName')}
                </Label>
                <p className="text-sm">{customer.first_name} {customer.last_name}</p>
              </div>
              
              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  <Mail className="h-4 w-4 inline mr-1" />
                  Email
                </Label>
                <p className="text-sm">{customer.email}</p>
              </div>

              {customer.phone && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">
                    <Phone className="h-4 w-4 inline mr-1" />
                    {t('customersPage.phone')}
                  </Label>
                  <p className="text-sm">{customer.phone}</p>
                </div>
              )}

              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  <Calendar className="h-4 w-4 inline mr-1" />
                  {t('customersPage.registered')}
                </Label>
                <p className="text-sm">
                  {new Date(customer.created_at).toLocaleDateString(
                    locale === 'it' ? 'it-CH' : locale === 'fr' ? 'fr-CH' : 'de-CH'
                  )}
                </p>
              </div>

              <Separator />

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">{t('customersPage.level')}:</span>
                  <Badge variant="secondary">{customer.current_level}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">{t('customersPage.points')}:</span>
                  <span className="text-sm font-medium">{customer.total_points}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">{t('customersPage.lifetimeSpend')}:</span>
                  <span className="text-sm font-medium">{formatCurrency(customer.lifetime_spend)}</span>
                </div>
              </div>

              <div className="flex gap-1 flex-wrap">
                {customer.current_level >= 3 && (
                  <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                    VIP
                  </Badge>
                )}
                {customer.is_admin && (
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                    Admin
                  </Badge>
                )}
                {stats.totalOrders > 0 ? (
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    {t('customersPage.active')}
                  </Badge>
                ) : (
                  <Badge variant="secondary" className="bg-gray-100 text-gray-800">
                    {t('customersPage.new')}
                  </Badge>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Statistics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                {t('customersPage.statistics')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">{t('customersPage.totalOrders')}:</span>
                <span className="text-sm font-medium">{stats.totalOrders}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">{t('customersPage.totalSpent')}:</span>
                <span className="text-sm font-medium">{formatCurrency(stats.totalSpent)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">{t('customersPage.avgOrderValue')}:</span>
                <span className="text-sm font-medium">{formatCurrency(stats.avgOrderValue)}</span>
              </div>
              {stats.lastOrderDate && (
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">{t('customersPage.lastOrder')}:</span>
                  <span className="text-sm font-medium">
                    {stats.lastOrderDate.toLocaleDateString(
                      locale === 'it' ? 'it-CH' : locale === 'fr' ? 'fr-CH' : 'de-CH'
                    )}
                  </span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Customer Addresses */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                {t('customersPage.addresses')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {addresses.length > 0 ? (
                <div className="space-y-4">
                  {addresses.map((address) => (
                    <div key={address.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <Building className="h-4 w-4" />
                          <span className="font-medium capitalize">
                            {t(`customersPage.${address.type}Address`)}
                          </span>
                          {address.is_default && (
                            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                              {t('customersPage.default')}
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="text-sm space-y-1">
                        <div className="font-medium">
                          {address.first_name} {address.last_name}
                        </div>
                        {address.company && (
                          <div className="text-muted-foreground">{address.company}</div>
                        )}
                        <div>{address.street_address}</div>
                        <div>{address.postal_code} {address.city}</div>
                        <div>{address.country}</div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4 text-muted-foreground">
                  {t('customersPage.noAddresses')}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Orders History */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                {t('customersPage.orderHistory')} ({orders.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {orders.length > 0 ? (
                <div className="space-y-4">
                  {orders.map((order) => (
                    <div key={order.id} className="border rounded-lg p-4 hover:bg-muted/50">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">
                            #{order.order_number || order.id.slice(-8)}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {new Date(order.created_at).toLocaleDateString(
                              locale === 'it' ? 'it-CH' : locale === 'fr' ? 'fr-CH' : 'de-CH'
                            )}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-medium">
                            {formatCurrency(order.total_amount)}
                          </div>
                          <Badge className={`${getStatusColor(order.status)} w-fit`}>
                            {order.status}
                          </Badge>
                        </div>
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`/${locale}/admin/orders/${order.id}`}>
                            {t('customersPage.viewOrder')}
                          </Link>
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  {t('customersPage.noOrders')}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
