'use client'

import { useEffect, useState, useCallback, useMemo } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useTranslations, useLocale } from 'next-intl'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { formatCurrency } from '@/lib/utils'
import { Search, Eye, Users, Crown, Star, Calendar } from 'lucide-react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { AdminBackButton } from '@/components/admin/admin-back-button'

export default function AdminCustomersPage() {
  const locale = useLocale()
  const router = useRouter()
  const t = useTranslations('admin')
  const supabase = useMemo(() => createClient(), [])

  const [loading, setLoading] = useState(true)
  const [authChecked, setAuthChecked] = useState(false)
  const [customersWithStats, setCustomersWithStats] = useState<Array<{
    id: string;
    first_name: string;
    last_name: string;
    email: string;
    phone?: string;
    created_at: string;
    lifetime_spend: number;
    current_level: number;
    total_points: number;
    totalOrders: number;
    totalSpent: number;
    lastOrderDate: Date | null;
    avgOrderValue: number;
  }>>([])

  const loadCustomersData = useCallback(async () => {
    try {
      console.log('Loading customers data...')

      // Get all customers first
      const { data: customers, error: customersError } = await supabase
        .from('users')
        .select(`
          id,
          first_name,
          last_name,
          email,
          phone,
          created_at,
          lifetime_spend,
          current_level,
          total_points
        `)
        .eq('is_admin', false)
        .order('created_at', { ascending: false })

      if (customersError) {
        console.error('Error fetching customers:', customersError)
        setLoading(false)
        return
      }

      if (!customers || customers.length === 0) {
        console.log('No customers found')
        setCustomersWithStats([])
        setLoading(false)
        return
      }

      // Get orders for all customers (exclude pending and cancelled orders from statistics)
      const customerIds = customers.map(c => c.id)
      const { data: orders, error: ordersError } = await supabase
        .from('orders')
        .select('user_id, total_amount, created_at')
        .in('user_id', customerIds)
        .not('status', 'in', '(pending,cancelled)')

      if (ordersError) {
        console.error('Error fetching orders:', ordersError)
        // Continue without orders data
      }

      // Calculate customer statistics
      const customersWithStatsCalc = customers.map(customer => {
        const customerOrders = orders?.filter(order => order.user_id === customer.id) || []
        const totalOrders = customerOrders.length
        const totalSpent = customerOrders.reduce((sum, order) => sum + (order.total_amount || 0), 0)
        const lastOrderDate = customerOrders.length > 0
          ? new Date(Math.max(...customerOrders.map(o => new Date(o.created_at).getTime())))
          : null
        const avgOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0

        return {
          ...customer,
          totalOrders,
          totalSpent,
          lastOrderDate,
          avgOrderValue
        }
      })

      setCustomersWithStats(customersWithStatsCalc)
      setLoading(false)
      console.log('Customers data loaded successfully')
    } catch (error) {
      console.error('Error loading customers data:', error)
      setLoading(false)
    }
  }, [supabase])

  useEffect(() => {
    async function checkAuthAndLoadData() {
      try {
        if (authChecked) return

        console.log('Checking authentication...')
        const { data: { user }, error: authError } = await supabase.auth.getUser()

        if (authError) {
          console.error('Auth error:', authError)
          setAuthChecked(true)
          router.push(`/${locale}/login`)
          return
        }

        if (!user) {
          console.log('No user found, redirecting to login')
          setAuthChecked(true)
          router.push(`/${locale}/login`)
          return
        }

        console.log('User found:', user.email)

        const { data: profile, error: profileError } = await supabase
          .from('users')
          .select('is_admin')
          .eq('id', user.id)
          .single()

        if (profileError) {
          console.error('Profile error:', profileError)
          setAuthChecked(true)
          setLoading(false)
          return
        }

        console.log('Profile loaded:', profile)

        if (!profile?.is_admin) {
          console.log('User is not admin, redirecting')
          setAuthChecked(true)
          router.push(`/${locale}`)
          return
        }

        console.log('User is admin, loading customers data')
        setAuthChecked(true)
        await loadCustomersData()
      } catch (error) {
        console.error('Error in checkAuthAndLoadData:', error)
        setAuthChecked(true)
        setLoading(false)
      }
    }

    if (!authChecked) {
      checkAuthAndLoadData()
    }
  }, [authChecked, locale, router, supabase, loadCustomersData])

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Caricamento clienti...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8">
      {/* Back to Dashboard Button */}
      <div className="mb-6">
        <AdminBackButton />
      </div>

      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">{t('customersPage.title')}</h1>
          <p className="text-muted-foreground">
            {t('customersPage.subtitle')}
          </p>
        </div>
      </div>

      {/* Search and Filters */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={t('customersPage.searchPlaceholder')}
                className="pl-10"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Customer Statistics */}
      <div className="grid md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('customersPage.totalCustomers')}</p>
                <p className="text-2xl font-bold">{customersWithStats.length}</p>
              </div>
              <Users className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('customersPage.vipCustomers')}</p>
                <p className="text-2xl font-bold">
                  {customersWithStats.filter(customer => customer.current_level >= 3).length}
                </p>
              </div>
              <Crown className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('customersPage.activeCustomers')}</p>
                <p className="text-2xl font-bold">
                  {customersWithStats.filter(customer => customer.totalOrders > 0).length}
                </p>
              </div>
              <Star className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('customersPage.newCustomers30Days')}</p>
                <p className="text-2xl font-bold">
                  {customersWithStats.filter(customer => {
                    const thirtyDaysAgo = new Date()
                    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
                    return new Date(customer.created_at) > thirtyDaysAgo
                  }).length}
                </p>
              </div>
              <Calendar className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Customers Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t('customersPage.allCustomers')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4">{t('customersPage.customer')}</th>
                  <th className="text-left py-3 px-4">{t('customersPage.registered')}</th>
                  <th className="text-left py-3 px-4">{t('customersPage.orders')}</th>
                  <th className="text-left py-3 px-4">{t('customersPage.totalRevenue')}</th>
                  <th className="text-left py-3 px-4">{t('customersPage.avgOrderValue')}</th>
                  <th className="text-left py-3 px-4">{t('customersPage.lastOrder')}</th>
                  <th className="text-left py-3 px-4">{t('customersPage.status')}</th>
                  <th className="text-left py-3 px-4">{t('customersPage.actions')}</th>
                </tr>
              </thead>
              <tbody>
                {customersWithStats.map((customer) => (
                  <tr key={customer.id} className="border-b hover:bg-muted/50">
                    <td className="py-3 px-4">
                      <div>
                        <div className="font-medium flex items-center gap-2">
                          {customer.first_name} {customer.last_name}
                          {customer.current_level >= 3 && <Crown className="h-4 w-4 text-yellow-500" />}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {customer.email}
                        </div>
                        {customer.phone && (
                          <div className="text-sm text-muted-foreground">
                            {customer.phone}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="text-sm">
                        {new Date(customer.created_at).toLocaleDateString(locale === 'it' ? 'it-IT' : locale === 'fr' ? 'fr-FR' : 'de-DE')}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="font-semibold">
                        {customer.totalOrders}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="font-semibold">
                        {formatCurrency(customer.totalSpent)}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="text-sm">
                        {customer.totalOrders > 0 ? formatCurrency(customer.avgOrderValue) : '-'}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="text-sm">
                        {customer.lastOrderDate
                          ? customer.lastOrderDate.toLocaleDateString(locale === 'it' ? 'it-IT' : locale === 'fr' ? 'fr-FR' : 'de-DE')
                          : t('customersPage.noOrders')
                        }
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex gap-1">
                        {customer.current_level >= 3 && (
                          <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                            VIP
                          </Badge>
                        )}
                        {customer.totalOrders > 0 ? (
                          <Badge variant="secondary" className="bg-green-100 text-green-800">
                            {t('customersPage.active')}
                          </Badge>
                        ) : (
                          <Badge variant="secondary" className="bg-gray-100 text-gray-800">
                            {t('customersPage.new')}
                          </Badge>
                        )}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <Button variant="ghost" size="sm" asChild>
                        <Link href={`/${locale}/admin/customers/${customer.id}`}>
                          <Eye className="h-4 w-4" />
                        </Link>
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            {customersWithStats.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                {t('customersPage.noCustomers')}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
