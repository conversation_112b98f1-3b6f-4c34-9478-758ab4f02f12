import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { analyticsService } from '@/lib/analytics/analytics-service'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()

    // Check if user is admin
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Parse request body
    const body = await request.json().catch(() => ({}))
    const targetDate = body.date ? new Date(body.date) : new Date()

    // Update analytics aggregation
    await analyticsService.updateAnalyticsAggregation(targetDate)

    return NextResponse.json({ 
      success: true, 
      message: 'Analytics aggregation updated successfully',
      date: targetDate.toISOString().split('T')[0]
    })
  } catch (error) {
    console.error('Error in analytics aggregation API:', error)
    return NextResponse.json({ 
      error: 'Failed to update analytics aggregation',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// GET endpoint to check aggregation status
export async function GET() {
  try {
    const supabase = await createClient()

    // Check if user is admin
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Get latest aggregation data
    const { data: latestAggregation } = await supabase
      .from('visitor_analytics')
      .select('date_period, period_type, updated_at')
      .eq('period_type', 'daily')
      .order('date_period', { ascending: false })
      .limit(1)
      .single()

    // Get total sessions and page visits for today
    const today = new Date().toISOString().split('T')[0]
    
    const { count: todaySessions } = await supabase
      .from('visitor_sessions')
      .select('*', { count: 'exact', head: true })
      .gte('first_visit_at', `${today}T00:00:00.000Z`)
      .lt('first_visit_at', `${today}T23:59:59.999Z`)

    const { count: todayPageViews } = await supabase
      .from('page_visits')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', `${today}T00:00:00.000Z`)
      .lt('created_at', `${today}T23:59:59.999Z`)

    return NextResponse.json({
      latestAggregation: latestAggregation || null,
      todayStats: {
        sessions: todaySessions || 0,
        pageViews: todayPageViews || 0,
        date: today
      },
      needsUpdate: !latestAggregation || latestAggregation.date_period !== today
    })
  } catch (error) {
    console.error('Error in analytics status API:', error)
    return NextResponse.json({ 
      error: 'Failed to get analytics status',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
