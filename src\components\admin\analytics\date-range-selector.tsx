'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useTranslations } from 'next-intl'
import { Calendar, ChevronDown, Clock } from 'lucide-react'

export type DateRange = 'today' | 'yesterday' | 'last7days' | 'last30days' | 'thisMonth' | 'lastMonth' | 'last3months' | 'last6months' | 'thisYear'

interface DateRangeOption {
  value: DateRange
  label: string
  description: string
}

interface DateRangeSelectorProps {
  selectedRange: DateRange
  onRangeChange: (range: DateRange) => void
  className?: string
}

export function DateRangeSelector({ selectedRange, onRangeChange, className = '' }: DateRangeSelectorProps) {
  const t = useTranslations('admin')
  const [isOpen, setIsOpen] = useState(false)

  const dateRangeOptions: DateRangeOption[] = [
    {
      value: 'today',
      label: t('analytics.dateRange.today') || 'Oggi',
      description: new Date().toLocaleDateString('it-IT', { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      })
    },
    {
      value: 'yesterday',
      label: t('analytics.dateRange.yesterday') || 'Ieri',
      description: new Date(Date.now() - 24 * 60 * 60 * 1000).toLocaleDateString('it-IT', { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      })
    },
    {
      value: 'last7days',
      label: t('analytics.dateRange.last7days') || 'Ultimi 7 giorni',
      description: 'Incluso oggi'
    },
    {
      value: 'last30days',
      label: t('analytics.dateRange.last30days') || 'Ultimi 30 giorni',
      description: 'Incluso oggi'
    },
    {
      value: 'thisMonth',
      label: t('analytics.dateRange.thisMonth') || 'Questo mese',
      description: new Date().toLocaleDateString('it-IT', { 
        year: 'numeric', 
        month: 'long' 
      })
    },
    {
      value: 'lastMonth',
      label: t('analytics.dateRange.lastMonth') || 'Mese scorso',
      description: new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1).toLocaleDateString('it-IT', { 
        year: 'numeric', 
        month: 'long' 
      })
    },
    {
      value: 'last3months',
      label: t('analytics.dateRange.last3months') || 'Ultimi 3 mesi',
      description: 'Incluso questo mese'
    },
    {
      value: 'last6months',
      label: t('analytics.dateRange.last6months') || 'Ultimi 6 mesi',
      description: 'Incluso questo mese'
    },
    {
      value: 'thisYear',
      label: t('analytics.dateRange.thisYear') || 'Quest\'anno',
      description: new Date().getFullYear().toString()
    }
  ]

  const selectedOption = dateRangeOptions.find(option => option.value === selectedRange)



  const isCurrentPeriod = (range: DateRange): boolean => {
    return ['today', 'thisMonth', 'thisYear'].includes(range)
  }

  return (
    <div className={`relative ${className}`}>
      <Button
        variant="outline"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full sm:w-auto justify-between min-w-[200px]"
      >
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4" />
          <span>{selectedOption?.label}</span>
          {isCurrentPeriod(selectedRange) && (
            <Badge variant="secondary" className="text-xs">
              <Clock className="h-3 w-3 mr-1" />
              Live
            </Badge>
          )}
        </div>
        <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </Button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-40" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <Card className="absolute top-full left-0 mt-2 w-80 z-50 shadow-lg">
            <CardContent className="p-2">
              <div className="space-y-1">
                {dateRangeOptions.map((option) => (
                  <Button
                    key={option.value}
                    variant={selectedRange === option.value ? "secondary" : "ghost"}
                    className="w-full justify-start h-auto p-3"
                    onClick={() => {
                      onRangeChange(option.value)
                      setIsOpen(false)
                    }}
                  >
                    <div className="text-left">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{option.label}</span>
                        {isCurrentPeriod(option.value) && (
                          <Badge variant="outline" className="text-xs">
                            Live
                          </Badge>
                        )}
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {option.description}
                      </div>
                    </div>
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  )
}
