'use client'

import { useState, useEffect, useMemo } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { createClient } from '@/lib/supabase/client'
import { useTranslations } from 'next-intl'
import { Languages, Globe, Users, TrendingUp } from 'lucide-react'
import { getFilteredVisitorSessions, getCurrentMonthRange } from '@/lib/analytics/admin-filter'

interface LanguageData {
  language: string
  visitors: number
  percentage: number
  displayName: string
  flag: string
}

interface VisitorSession {
  browser_language: string | null
}

interface LanguageAnalyticsProps {
  className?: string
  showTopCount?: number
}

// Language display names and flags
const languageMap: Record<string, { name: string; flag: string }> = {
  'de': { name: 'Deutsch', flag: '🇩🇪' },
  'de-DE': { name: '<PERSON><PERSON><PERSON>', flag: '🇩🇪' },
  'de-CH': { name: '<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON><PERSON>)', flag: '🇨🇭' },
  'it': { name: 'Italiano', flag: '🇮🇹' },
  'it-IT': { name: 'Italiano', flag: '🇮🇹' },
  'it-CH': { name: 'Italiano (Svizzera)', flag: '🇨🇭' },
  'fr': { name: 'Français', flag: '🇫🇷' },
  'fr-FR': { name: 'Français', flag: '🇫🇷' },
  'fr-CH': { name: 'Français (Suisse)', flag: '🇨🇭' },
  'en': { name: 'English', flag: '🇬🇧' },
  'en-US': { name: 'English (US)', flag: '🇺🇸' },
  'en-GB': { name: 'English (UK)', flag: '🇬🇧' },
  'es': { name: 'Español', flag: '🇪🇸' },
  'pt': { name: 'Português', flag: '🇵🇹' },
  'ru': { name: 'Русский', flag: '🇷🇺' },
  'zh': { name: '中文', flag: '🇨🇳' },
  'ja': { name: '日本語', flag: '🇯🇵' },
  'ar': { name: 'العربية', flag: '🇸🇦' },
  'unknown': { name: 'Sconosciuto', flag: '❓' }
}

const getLanguageInfo = (langCode: string): { name: string; flag: string } => {
  // Try exact match first
  if (languageMap[langCode]) {
    return languageMap[langCode]
  }
  
  // Try base language (e.g., 'de' from 'de-AT')
  const baseLang = langCode.split('-')[0]
  if (languageMap[baseLang]) {
    return languageMap[baseLang]
  }
  
  // Default to unknown
  return languageMap['unknown']
}

export function LanguageAnalytics({ className = '', showTopCount = 8 }: LanguageAnalyticsProps) {
  const t = useTranslations('admin')
  const supabase = useMemo(() => createClient(), [])
  const [loading, setLoading] = useState(true)
  const [languageData, setLanguageData] = useState<LanguageData[]>([])
  const [totalVisitors, setTotalVisitors] = useState(0)

  useEffect(() => {
    const loadLanguageAnalytics = async () => {
      try {
        setLoading(true)

        // Get current month date range
        const currentMonthRange = getCurrentMonthRange()

        // Get current month visitor sessions with language data (with admin filtering)
        const { data: sessions, error } = await getFilteredVisitorSessions(supabase, currentMonthRange, {}, 'browser_language')

        if (error) {
          console.error('Error loading language analytics:', error)
          setLanguageData([])
          setTotalVisitors(0)
          return
        }

        if (!sessions) {
          setLanguageData([])
          setTotalVisitors(0)
          return
        }

        // Count visitors by language
        const languageCounts: Record<string, number> = {}
        sessions.forEach((session: VisitorSession) => {
          const lang = session.browser_language || 'unknown'
          languageCounts[lang] = (languageCounts[lang] || 0) + 1
        })

        const total = sessions.length
        setTotalVisitors(total)

        // Convert to array and sort by count
        const languageArray = Object.entries(languageCounts)
          .map(([language, visitors]) => {
            const langInfo = getLanguageInfo(language)
            return {
              language,
              visitors,
              percentage: total > 0 ? (visitors / total) * 100 : 0,
              displayName: langInfo.name,
              flag: langInfo.flag
            }
          })
          .sort((a, b) => b.visitors - a.visitors)

        // Get top languages and group others
        const topLanguages = languageArray.slice(0, showTopCount)
        const otherLanguages = languageArray.slice(showTopCount)
        
        if (otherLanguages.length > 0) {
          const othersTotal = otherLanguages.reduce((sum, lang) => sum + lang.visitors, 0)
          const othersPercentage = total > 0 ? (othersTotal / total) * 100 : 0
          
          topLanguages.push({
            language: 'others',
            visitors: othersTotal,
            percentage: othersPercentage,
            displayName: t('analytics.others') || 'Altri',
            flag: '🌐'
          })
        }

        setLanguageData(topLanguages)
      } catch (error) {
        console.error('Error loading language analytics:', error)
        setLanguageData([])
        setTotalVisitors(0)
      } finally {
        setLoading(false)
      }
    }

    loadLanguageAnalytics()
  }, [supabase, showTopCount, t])

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Languages className="h-5 w-5" />
            <div className="animate-pulse h-5 bg-gray-200 rounded w-48"></div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="flex justify-between items-center mb-2">
                  <div className="h-4 bg-gray-200 rounded w-24"></div>
                  <div className="h-4 bg-gray-200 rounded w-16"></div>
                </div>
                <div className="h-2 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Languages className="h-5 w-5" />
          {t('analytics.languageBreakdown') || 'Ripartizione per Lingua'}
        </CardTitle>
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Users className="h-4 w-4" />
          <span>
            {totalVisitors.toLocaleString()} {t('analytics.monthlyVisitors') || 'visitatori mensili'}
          </span>
        </div>
      </CardHeader>
      <CardContent>
        {languageData.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Globe className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>{t('analytics.noLanguageData') || 'Nessun dato linguistico disponibile'}</p>
          </div>
        ) : (
          <div className="space-y-4">
            {languageData.map((lang, index) => (
              <div key={lang.language} className="space-y-2">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <span className="text-lg">{lang.flag}</span>
                    <span className="font-medium">{lang.displayName}</span>
                    {index === 0 && (
                      <Badge variant="secondary" className="text-xs">
                        <TrendingUp className="h-3 w-3 mr-1" />
                        {t('analytics.topLanguage') || 'Top'}
                      </Badge>
                    )}
                  </div>
                  <div className="text-right">
                    <div className="font-semibold">
                      {lang.visitors.toLocaleString()}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {lang.percentage.toFixed(1)}%
                    </div>
                  </div>
                </div>
                <Progress 
                  value={lang.percentage} 
                  className="h-2"
                />
              </div>
            ))}
          </div>
        )}
        
        {languageData.length > 0 && (
          <div className="mt-6 pt-4 border-t">
            <div className="text-sm text-muted-foreground">
              <p className="mb-2">
                <strong>{t('analytics.insights') || 'Insights'}:</strong>
              </p>
              <ul className="space-y-1 text-xs">
                {languageData[0] && (
                  <li>
                    • {languageData[0].displayName} è la lingua principale ({languageData[0].percentage.toFixed(1)}%)
                  </li>
                )}
                {languageData.length > 1 && (
                  <li>
                    • {languageData.length - (languageData.find(l => l.language === 'others') ? 1 : 0)} lingue diverse rilevate
                  </li>
                )}
                <li>
                  • {totalVisitors.toLocaleString()} visitatori totali questo mese
                </li>
              </ul>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
