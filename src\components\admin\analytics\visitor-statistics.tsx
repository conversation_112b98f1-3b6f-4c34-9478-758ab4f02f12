'use client'

import { useState, useEffect, useMemo } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { createClient } from '@/lib/supabase/client'
import { useTranslations } from 'next-intl'
import { getFilteredVisitorSessions, getFilteredPageVisits, getCurrentMonthRange, getLastMonthRange } from '@/lib/analytics/admin-filter'

import {
  Users,
  Eye,
  TrendingUp,
  TrendingDown,
  Calendar,
  Globe,
  Clock
} from 'lucide-react'

interface VisitorStats {
  totalVisitors: number
  totalPageViews: number
  uniqueVisitors: number
  bounceRate: number
  avgSessionDuration: number
  monthlyVisitors: number
  lastMonthVisitors: number
  monthlyPageViews: number
  lastMonthPageViews: number
}

interface SessionData {
  session_id: string
  page_views_count: number | null
  is_bounce: boolean | null
  session_duration_seconds: number | null
}

interface VisitorStatisticsProps {
  className?: string
}

export function VisitorStatistics({ className = '' }: VisitorStatisticsProps) {
  const t = useTranslations('admin')
  const supabase = useMemo(() => createClient(), [])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [stats, setStats] = useState<VisitorStats>({
    totalVisitors: 0,
    totalPageViews: 0,
    uniqueVisitors: 0,
    bounceRate: 0,
    avgSessionDuration: 0,
    monthlyVisitors: 0,
    lastMonthVisitors: 0,
    monthlyPageViews: 0,
    lastMonthPageViews: 0
  })

  useEffect(() => {
    const loadVisitorStats = async () => {
      try {
        setLoading(true)

        // Get date ranges
        const currentMonthRange = getCurrentMonthRange()
        const lastMonthRange = getLastMonthRange()

        // Get current month sessions (with admin filtering)
        const currentMonthSessionsQuery = await getFilteredVisitorSessions(supabase, currentMonthRange)
        const { data: currentMonthSessions } = await currentMonthSessionsQuery

        // Get last month sessions (with admin filtering)
        const lastMonthSessionsQuery = await getFilteredVisitorSessions(supabase, lastMonthRange)
        const { data: lastMonthSessions } = await lastMonthSessionsQuery

        // Get all-time sessions (with admin filtering)
        const allTimeRange = { start: new Date('2020-01-01'), end: new Date() }
        const allTimeSessionsQuery = await getFilteredVisitorSessions(supabase, allTimeRange)
        const { data: allTimeSessions } = await allTimeSessionsQuery

        // Get current month page views (with admin filtering)
        const currentMonthPageViewsQuery = await getFilteredPageVisits(supabase, currentMonthRange)
        const { data: currentMonthPageViews } = await currentMonthPageViewsQuery

        // Get last month page views (with admin filtering)
        const lastMonthPageViewsQuery = await getFilteredPageVisits(supabase, lastMonthRange)
        const { data: lastMonthPageViews } = await lastMonthPageViewsQuery



        // Calculate statistics
        const currentMonthVisitors = currentMonthSessions?.length || 0
        const lastMonthVisitorsCount = lastMonthSessions?.length || 0
        const totalVisitors = allTimeSessions?.length || 0
        const totalPageViews = Array.isArray(allTimeSessions)
          ? allTimeSessions.reduce((sum, session: unknown) => sum + ((session as SessionData).page_views_count || 0), 0)
          : 0
        const uniqueVisitors = Array.isArray(allTimeSessions)
          ? new Set(allTimeSessions.map((s: unknown) => (s as SessionData).session_id)).size
          : 0

        // Calculate bounce rate and avg session duration for current month
        const bounceRate = Array.isArray(currentMonthSessions) && currentMonthSessions.length
          ? (currentMonthSessions.filter((s: unknown) => (s as SessionData).is_bounce).length / currentMonthSessions.length) * 100
          : 0

        const avgSessionDuration = Array.isArray(currentMonthSessions) && currentMonthSessions.length
          ? currentMonthSessions.reduce((sum, s: unknown) => sum + ((s as SessionData).session_duration_seconds || 0), 0) / currentMonthSessions.length
          : 0

        setStats({
          totalVisitors,
          totalPageViews,
          uniqueVisitors,
          bounceRate: Math.round(bounceRate * 100) / 100,
          avgSessionDuration: Math.round(avgSessionDuration),
          monthlyVisitors: currentMonthVisitors,
          lastMonthVisitors: lastMonthVisitorsCount,
          monthlyPageViews: currentMonthPageViews?.length || 0,
          lastMonthPageViews: lastMonthPageViews?.length || 0
        })
      } catch (error) {
        console.error('Error loading visitor statistics:', error)
        setError(error instanceof Error ? error.message : 'Failed to load visitor statistics')
      } finally {
        setLoading(false)
      }
    }

    loadVisitorStats()
  }, [supabase])

  // Calculate growth percentages
  const visitorGrowth = stats.lastMonthVisitors > 0 
    ? ((stats.monthlyVisitors - stats.lastMonthVisitors) / stats.lastMonthVisitors) * 100 
    : 0

  const pageViewGrowth = stats.lastMonthPageViews > 0 
    ? ((stats.monthlyPageViews - stats.lastMonthPageViews) / stats.lastMonthPageViews) * 100 
    : 0

  // Format duration
  const formatDuration = (seconds: number): string => {
    if (seconds < 60) return `${seconds}s`
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}m ${remainingSeconds}s`
  }

  if (loading) {
    return (
      <div className={`grid md:grid-cols-2 lg:grid-cols-4 gap-4 ${className}`}>
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardContent className="pt-6">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/4"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <div className={`grid md:grid-cols-2 lg:grid-cols-4 gap-4 ${className}`}>
        <Card className="col-span-full">
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <div className="text-red-500 mb-2">
                <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <p className="text-sm text-muted-foreground">{error}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className={`grid md:grid-cols-2 lg:grid-cols-4 gap-4 ${className}`}>
      {/* Monthly Visitors */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                {t('analytics.monthlyVisitors') || 'Visitatori Mensili'}
              </p>
              <p className="text-2xl font-bold">{stats.monthlyVisitors.toLocaleString()}</p>
              <div className="flex items-center gap-1 mt-1">
                {visitorGrowth >= 0 ? (
                  <TrendingUp className="h-4 w-4 text-green-500" />
                ) : (
                  <TrendingDown className="h-4 w-4 text-red-500" />
                )}
                <span className={`text-sm ${visitorGrowth >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                  {Math.abs(visitorGrowth).toFixed(1)}%
                </span>
              </div>
            </div>
            <Users className="h-8 w-8 text-muted-foreground" />
          </div>
        </CardContent>
      </Card>

      {/* Total Visitors */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                {t('analytics.totalVisitors') || 'Visitatori Totali'}
              </p>
              <p className="text-2xl font-bold">{stats.totalVisitors.toLocaleString()}</p>
              <p className="text-sm text-muted-foreground mt-1">
                {t('analytics.allTime') || 'Tutti i tempi'}
              </p>
            </div>
            <Globe className="h-8 w-8 text-muted-foreground" />
          </div>
        </CardContent>
      </Card>

      {/* Monthly Page Views */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                {t('analytics.monthlyPageViews') || 'Visualizzazioni Mensili'}
              </p>
              <p className="text-2xl font-bold">{stats.monthlyPageViews.toLocaleString()}</p>
              <div className="flex items-center gap-1 mt-1">
                {pageViewGrowth >= 0 ? (
                  <TrendingUp className="h-4 w-4 text-green-500" />
                ) : (
                  <TrendingDown className="h-4 w-4 text-red-500" />
                )}
                <span className={`text-sm ${pageViewGrowth >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                  {Math.abs(pageViewGrowth).toFixed(1)}%
                </span>
              </div>
            </div>
            <Eye className="h-8 w-8 text-muted-foreground" />
          </div>
        </CardContent>
      </Card>

      {/* Session Metrics */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                {t('analytics.sessionMetrics') || 'Metriche Sessione'}
              </p>
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="text-xs">
                    {stats.bounceRate.toFixed(1)}% {t('analytics.bounce') || 'Rimbalzo'}
                  </Badge>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-3 w-3 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">
                    {formatDuration(stats.avgSessionDuration)}
                  </span>
                </div>
              </div>
            </div>
            <Calendar className="h-8 w-8 text-muted-foreground" />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
