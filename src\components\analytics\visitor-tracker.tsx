'use client'

import { useEffect, useRef, useCallback } from 'react'
import { usePathname } from 'next/navigation'
import { useLocale } from 'next-intl'
import { createClient } from '@/lib/supabase/client'
import { isCurrentUserExcluded } from '@/lib/analytics/admin-filter'

interface VisitorTrackerProps {
  enabled?: boolean
}

// Generate a unique session ID
const generateSessionId = (): string => {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// Get browser language
const getBrowserLanguage = (): string => {
  if (typeof window === 'undefined') return 'unknown'
  return navigator.language || navigator.languages?.[0] || 'unknown'
}

// Get page title
const getPageTitle = (): string => {
  if (typeof window === 'undefined') return ''
  return document.title || ''
}

// Get referrer
const getReferrer = (): string => {
  if (typeof window === 'undefined') return ''
  return document.referrer || ''
}



// Check if analytics cookies are enabled
const isAnalyticsEnabled = (): boolean => {
  if (typeof window === 'undefined') return false

  const consent = localStorage.getItem('cookie-consent')
  if (!consent) return false

  try {
    if (consent === 'necessary') return false
    if (consent === 'all') return true

    const settings = JSON.parse(consent)
    return settings.analytics === true
  } catch {
    return false
  }
}

export function VisitorTracker({ enabled = true }: VisitorTrackerProps) {
  const pathname = usePathname()
  const locale = useLocale()
  const supabase = createClient()
  const sessionIdRef = useRef<string | null>(null)
  const pageStartTimeRef = useRef<number>(Date.now())
  const lastPathRef = useRef<string>('')

  // Initialize session ID
  useEffect(() => {
    if (!sessionIdRef.current) {
      sessionIdRef.current = generateSessionId()
    }
  }, [])

  // Track page visit
  const trackPageVisit = useCallback(async (pagePath: string) => {
    if (!enabled || !isAnalyticsEnabled() || !sessionIdRef.current) return

    // Check if user should be excluded from analytics
    if (await isCurrentUserExcluded(supabase)) return

    try {
      const browserLanguage = getBrowserLanguage()
      const pageTitle = getPageTitle()
      const referrer = getReferrer()

      // Get current user ID if available
      const { data: { user } } = await supabase.auth.getUser()
      const userId = user?.id || null

      // Track page visit
      await supabase.from('page_visits').insert({
        session_id: sessionIdRef.current,
        page_path: pagePath,
        page_title: pageTitle,
        page_locale: locale,
        browser_language: browserLanguage,
        referrer: referrer,
        visit_duration_seconds: 0
      })

      // Update or create session
      const { data: existingSession } = await supabase
        .from('visitor_sessions')
        .select('id, page_views_count')
        .eq('session_id', sessionIdRef.current)
        .single()

      if (existingSession) {
        // Update existing session (including user_id if user logged in during session)
        await supabase
          .from('visitor_sessions')
          .update({
            user_id: userId, // Update user_id in case user logged in during session
            last_activity_at: new Date().toISOString(),
            page_views_count: existingSession.page_views_count + 1,
            is_bounce: existingSession.page_views_count === 0, // Still bounce if this is second page
            updated_at: new Date().toISOString()
          })
          .eq('session_id', sessionIdRef.current)
      } else {
        // Create new session
        await supabase.from('visitor_sessions').insert({
          session_id: sessionIdRef.current,
          user_id: userId,
          browser_language: browserLanguage,
          user_agent: navigator.userAgent,
          referrer: referrer,
          first_visit_at: new Date().toISOString(),
          last_activity_at: new Date().toISOString(),
          page_views_count: 1,
          is_bounce: true,
          session_duration_seconds: 0
        })
      }
    } catch (error) {
      console.error('Error tracking page visit:', error)
    }
  }, [enabled, locale, supabase])

  // Update page visit duration when leaving
  const updatePageDuration = useCallback(async (pagePath: string) => {
    if (!enabled || !isAnalyticsEnabled() || !sessionIdRef.current) return

    // Check if user should be excluded from analytics
    if (await isCurrentUserExcluded(supabase)) return

    try {
      const duration = Math.floor((Date.now() - pageStartTimeRef.current) / 1000)
      
      // Update the most recent page visit for this session and path
      await supabase
        .from('page_visits')
        .update({ visit_duration_seconds: duration })
        .eq('session_id', sessionIdRef.current)
        .eq('page_path', pagePath)
        .order('created_at', { ascending: false })
        .limit(1)
    } catch (error) {
      console.error('Error updating page duration:', error)
    }
  }, [enabled, supabase])

  // Track page changes
  useEffect(() => {
    const handlePageChange = async () => {
      if (!enabled || !isAnalyticsEnabled()) return

      // Check if user should be excluded from analytics
      if (await isCurrentUserExcluded(supabase)) return

      // Update duration for previous page
      if (lastPathRef.current && lastPathRef.current !== pathname) {
        updatePageDuration(lastPathRef.current)
      }

      // Track new page visit
      trackPageVisit(pathname)

      // Update refs
      lastPathRef.current = pathname
      pageStartTimeRef.current = Date.now()
    }

    handlePageChange()
  }, [pathname, enabled, trackPageVisit, updatePageDuration])

  // Handle page unload
  useEffect(() => {
    if (!enabled || !isAnalyticsEnabled()) return

    const handleBeforeUnload = () => {
      if (lastPathRef.current) {
        // Use sendBeacon for reliable tracking on page unload
        const duration = Math.floor((Date.now() - pageStartTimeRef.current) / 1000)
        
        if (navigator.sendBeacon && sessionIdRef.current) {
          const data = new FormData()
          data.append('session_id', sessionIdRef.current)
          data.append('page_path', lastPathRef.current)
          data.append('duration', duration.toString())
          
          // This would need a corresponding API endpoint
          navigator.sendBeacon('/api/analytics/page-duration', data)
        }
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => window.removeEventListener('beforeunload', handleBeforeUnload)
  }, [enabled])

  // Update session duration periodically
  useEffect(() => {
    if (!enabled || !isAnalyticsEnabled() || !sessionIdRef.current) return

    const updateSessionDuration = async () => {
      // Check if user should be excluded from analytics
      if (await isCurrentUserExcluded(supabase)) return

      try {
        const { data: session } = await supabase
          .from('visitor_sessions')
          .select('first_visit_at')
          .eq('session_id', sessionIdRef.current)
          .single()

        if (session) {
          const sessionStart = new Date(session.first_visit_at).getTime()
          const duration = Math.floor((Date.now() - sessionStart) / 1000)
          
          await supabase
            .from('visitor_sessions')
            .update({
              session_duration_seconds: duration,
              last_activity_at: new Date().toISOString(),
              is_bounce: duration < 30 // Consider < 30 seconds as bounce
            })
            .eq('session_id', sessionIdRef.current)
        }
      } catch (error) {
        console.error('Error updating session duration:', error)
      }
    }

    // Update every 30 seconds
    const interval = setInterval(updateSessionDuration, 30000)
    return () => clearInterval(interval)
  }, [enabled, supabase])

  return null // This component doesn't render anything
}
