import { createClient } from '@/lib/supabase/client'
import type { SupabaseClient } from '@supabase/supabase-js'

/**
 * Centralized admin filtering utility for analytics
 * Provides consistent admin user exclusion across all analytics components
 */

// Cache for admin user IDs to avoid repeated database queries
let adminUserIdsCache: string[] | null = null
let cacheTimestamp: number = 0
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

/**
 * Get all admin user IDs with caching
 */
export async function getAdminUserIds(supabase?: SupabaseClient): Promise<string[]> {
  const now = Date.now()
  
  // Return cached result if still valid
  if (adminUserIdsCache && (now - cacheTimestamp) < CACHE_DURATION) {
    return adminUserIdsCache
  }

  try {
    const client = supabase || createClient()
    const { data: adminUsers, error } = await client
      .from('users')
      .select('id')
      .eq('is_admin', true)

    if (error) {
      console.error('Error fetching admin users:', error)
      return adminUserIdsCache || []
    }

    adminUserIdsCache = adminUsers?.map(user => user.id) || []
    cacheTimestamp = now
    
    return adminUserIdsCache
  } catch (error) {
    console.error('Error in getAdminUserIds:', error)
    return adminUserIdsCache || []
  }
}

/**
 * Clear the admin user IDs cache
 */
export function clearAdminUserCache(): void {
  adminUserIdsCache = null
  cacheTimestamp = 0
}

/**
 * Check if a user ID belongs to an admin user
 */
export async function isAdminUser(userId: string, supabase?: SupabaseClient): Promise<boolean> {
  const adminIds = await getAdminUserIds(supabase)
  return adminIds.includes(userId)
}

/**
 * Get Supabase filter for excluding admin sessions
 * Returns a filter that excludes sessions where user_id is an admin
 */
export async function getAdminExclusionFilter(supabase?: SupabaseClient): Promise<string> {
  const adminIds = await getAdminUserIds(supabase)
  
  if (adminIds.length === 0) {
    return 'user_id.is.null,user_id.neq.null' // Include all sessions if no admins found
  }
  
  // Include sessions where user_id is null (anonymous) OR user_id is not in admin list
  return `user_id.is.null,user_id.not.in.(${adminIds.join(',')})`
}

/**
 * Filter page visits to exclude admin users
 * Uses efficient database-level filtering with session joins
 */
export async function getFilteredPageVisits(
  supabase: SupabaseClient,
  dateRange: { start: Date; end: Date },
  additionalFilters?: Record<string, string | number | boolean>,
  selectColumns: string = `
      *,
      visitor_sessions!inner(user_id)
    `
) {
  const adminIds = await getAdminUserIds(supabase)

  let query = supabase
    .from('page_visits')
    .select(selectColumns)
    .gte('created_at', dateRange.start.toISOString())
    .lte('created_at', dateRange.end.toISOString())

  // Exclude admin users at database level
  if (adminIds.length > 0) {
    query = query.or(`visitor_sessions.user_id.is.null,visitor_sessions.user_id.not.in.(${adminIds.join(',')})`)
  }

  // Apply additional filters
  if (additionalFilters) {
    Object.entries(additionalFilters).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== 'all') {
        query = query.eq(key, value)
      }
    })
  }

  return query
}

/**
 * Filter visitor sessions to exclude admin users
 */
export async function getFilteredVisitorSessions(
  supabase: SupabaseClient,
  dateRange: { start: Date; end: Date },
  additionalFilters?: Record<string, string | number | boolean>,
  selectColumns: string = '*'
) {
  const adminExclusionFilter = await getAdminExclusionFilter(supabase)

  let query = supabase
    .from('visitor_sessions')
    .select(selectColumns)
    .gte('first_visit_at', dateRange.start.toISOString())
    .lte('first_visit_at', dateRange.end.toISOString())
    .or(adminExclusionFilter)

  // Apply additional filters
  if (additionalFilters) {
    Object.entries(additionalFilters).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== 'all') {
        query = query.eq(key, value)
      }
    })
  }

  return query
}

/**
 * Check if current authenticated user should be excluded from analytics tracking
 */
export async function isCurrentUserExcluded(supabase?: SupabaseClient): Promise<boolean> {
  try {
    const client = supabase || createClient()
    const { data: { user } } = await client.auth.getUser()
    
    if (!user?.id) return false
    
    return await isAdminUser(user.id, client)
  } catch (error) {
    console.error('Error checking if current user is excluded:', error)
    return false
  }
}

/**
 * Get current month date range
 */
export function getCurrentMonthRange(): { start: Date; end: Date } {
  const now = new Date()
  const start = new Date(now.getFullYear(), now.getMonth(), 1)
  const end = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999)
  
  return { start, end }
}

/**
 * Get last month date range
 */
export function getLastMonthRange(): { start: Date; end: Date } {
  const now = new Date()
  const start = new Date(now.getFullYear(), now.getMonth() - 1, 1)
  const end = new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59, 999)
  
  return { start, end }
}
